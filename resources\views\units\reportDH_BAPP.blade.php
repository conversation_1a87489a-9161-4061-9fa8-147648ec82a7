<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Berita Acara Pemakaian Part</title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 20px;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 5px;
        }

        .logo-left {
            float: left;
            width: 20%;
        }

        .title-center {
            float: left;
            width: 60%;
            text-align: center;
        }

        .number-right {
            float: right;
            width: 20%;
            text-align: right;
        }

        .header-title {
            background-color: rgb(29, 87, 138);
            color: white;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        /* Customer info table */
        .customer-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .customer-table td {
            border: 1px solid #000;
            padding: 1px;
            padding-left: 4px;
        }

        .customer-table .label {
            width: 10%;
            font-weight: normal;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color: rgb(29, 87, 138);
            color: white;
            font-weight: bold;
        }

        /* Total section */
        .total-section {
            width: 30%;
            float: right;
            margin-bottom: 20px;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
        }

        .total-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: left;
        }

        .total-table .total-label {
            font-weight: bold;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .signature-table td {
            width: 33.33%;
            text-align: center;
            vertical-align: bottom;
            font-weight: bold;
            font-size: 10px;
        }

        .signature-stamp {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        /* Adjust for landscape orientation */
        @page {
            size: landscape;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 5px;
        }

        .logo-left {
            float: left;
            width: 20%;
        }

        .title-center {
            float: left;
            width: 60%;
            text-align: center;
        }

        .number-right {
            float: right;
            width: 20%;
            text-align: right;
        }

        .header-title {
            background-color: rgb(29, 87, 138);
            color: white;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        .customer-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .customer-table td {
            border: 1px solid #000;
            padding: 1px;
            padding-left: 4px;
        }

        .customer-table .label {
            width: 10%;
            font-weight: normal;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color: rgb(29, 87, 138);
            color: white;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <!-- Header with logo, title and number -->
    <!-- Header with logo, title and number -->
    <div class="header-container clearfix">
        <div class="logo-left" style="text-align: center;">
            <img src="{{ public_path('assets/images/logo-small.png') }}" alt="PWB LOGO" style="width: 80px;">
            <p><strong>PT PUTERA WIBOWO BORNEO</strong></p>
        </div>
        <div class="title-center">
            <div class="header-title">BERITA ACARA PEMAKAIAN PART SITE DH KINTAP</div>
        </div>
        <div class="number-right" style="padding-top: 60px;">
            <div
                style="font-size: 14px; font-weight: bold; padding: 2px 2px 2px 10px; text-align: left; background-color:rgb(29, 87, 138); color: white;">
                DO : {{ $transactions[0]->do_number ?? '' }}</div>
            <div
                style="font-size: 14px; font-weight: bold; border-top: 1px solid #fff5f5; padding: 2px 2px 2px 10px; text-align: left; background-color:rgb(29, 87, 138); color: white;">
                PO : {{ $transactions[0]->po_number ?? '' }}</div>
        </div>
    </div>


    <!-- Customer Information -->
    <table class="customer-table">
        <tr style="width: 20%;">
            <td class="label">CUSTOMER</td>
            <td>PT. DARMA HENWA</td>
            <td class="label">CONTACT</td>
            <td style="width: 20%;">{{ $transactions[0]->contact ?? '' }}</td>
        </tr>
        <tr style="width: 100%;">
            <td class="label">CODE AREA</td>
            <td>SITE KINTAP</td>
            <td class="label">PHONE</td>
            <td>0813-4528-0221</td>
        </tr>
        <tr>
            <td class="label">PAYMENT</td>
            <td>30 DAYS</td>
            <td class="label">DATE CREATED</td>
            <td style="width: 20%;">
                {{ \Carbon\Carbon::parse($transactions[0]->mr_date ?? \Carbon\Carbon::now())->locale('id')->isoFormat('D MMMM Y') }}
            </td>
        </tr>
    </table>


    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th rowspan="2" style="width:3%;">NO</th>
                <th colspan="2" style="width:18%;">SLIP STORE</th> <!-- Gabungan IREQ dan DATE -->
                <th rowspan="2" style="width:10%;">UNIT</th>
                <th rowspan="2" style="width:14%;">PART NUMBER</th>
                <th rowspan="2" style="width:28%;">DESCRIPTION</th>
                <th rowspan="2" style="width:4%;">QTY</th>
                <th rowspan="2" style="width:4%;">UOM</th>
                <th rowspan="2" style="width:10%;">UNIT PRICE</th>
                <th rowspan="2" style="width:13%;">TOTAL PRICE</th>
            </tr>
            <tr>
                <th style="width:10%;">IREQ</th>
                <th style="width:8%;">DATE</th>
            </tr>
        </thead>
        <tbody>
            @if(count($transactions) > 0)
                @php
                    $no = 1;
                    $totalAmount = 0;
                    $sortedTransactions = $transactions->sortBy(function ($transaction) {
                        return $transaction->mr_date ?? '9999-12-31';
                    });
                @endphp

                @foreach($sortedTransactions as $transaction)
                    @php
                        $transactionDate = \Carbon\Carbon::parse($transaction->mr_date ?? now())->translatedFormat('d F Y');
                        $sortedParts = $transaction->parts->sortBy('id');
                        $partCount = $sortedParts->count();
                        $rowspan = $partCount > 0 ? $partCount : 1;
                        $firstRow = true;
                    @endphp

                    @foreach($sortedParts as $part)
                        @php
                            $subtotal = $part->price * $part->quantity;
                            $totalAmount += $subtotal;
                        @endphp
                        <tr>
                            <td>{{ $no++ }}</td>

                            @if($firstRow)
                                <td rowspan="{{ $rowspan }}">{{ $transaction->noireq ?? '-' }}</td>
                                <td rowspan="{{ $rowspan }}">{{ $transactionDate }}</td>
                            @endif

                            <td>{{ $transaction->unit->unit_code }}</td>
                            <td>{{ $part->partInventory->part->part_code }}</td>
                            <td>{{ $part->partInventory->part->part_name }}</td>
                            <td>{{ $part->quantity }}</td>
                            <td>{{ $part->partInventory->oum ?? $part->eum }}</td>
                            <td style="text-align: right;">
                                <span style="float: left; padding-left: 4px">Rp</span>
                                {{ number_format($part->price, 0, ',', '.') }}
                            </td>
                            <td style="text-align: right;">
                                <span style="float: left; padding-left: 4px">Rp</span> {{ number_format($subtotal, 0, ',', '.') }}
                            </td>
                        </tr>
                        @php $firstRow = false; @endphp
                    @endforeach

                    @if($sortedParts->isEmpty())
                        <tr>
                            <td>{{ $no++ }}</td>
                            <td rowspan="1">{{ $transaction->noireq ?? '-' }}</td>
                            <td rowspan="1">{{ $transactionDate }}</td>
                            <td>{{ $transaction->unit->unit_code }}</td>
                            <td>-</td>
                            <td>-</td>
                            <td>0</td>
                            <td>-</td>
                            <td style="text-align: right;">Rp 0</td>
                            <td style="text-align: right;">Rp 0</td>
                        </tr>
                    @endif

                @endforeach
            @else
                @for($i = 1; $i <= 10; $i++)
                    <tr>
                        <td>{{ $i }}</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                @endfor
            @endif

            <tr>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="text-align: left;">TOTAL</td>
                <td style="text-align: right;">
                    <span style="float: left; padding-left: 4px">Rp</span>
                    {{ number_format($totalAmount ?? 0, 0, ',', '.') }}
                </td>
            </tr>

            <tr style="background-color: #ffff">
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="text-align: left;">PPN 11%</td>
                <td style="text-align: right;">
                    <span style="float: left; padding-left: 4px">Rp</span>
                    {{ number_format(($totalAmount ?? 0) * 0.11, 0, ',', '.') }}
                </td>
            </tr>

            <tr>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="text-align: left;">GRAND TOTAL</td>
                <td style="text-align: right;">
                    <span style="float: left; padding-left: 4px">Rp</span>
                    {{ number_format(($totalAmount ?? 0) * 1.11, 0, ',', '.') }}
                </td>
            </tr>
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="clearfix">
        <table class="signature-table">
            <tr>
                <td>
                    <div>Approve,</div>
                </td>
                <td>
                    <div>Approve,</div>
                </td>
                <td>
                    <div>Approve,</div>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 60px;">
                    <p style="padding: 0;margin: 0;">{{session('name')}}</p>
                    <div>(ADMIN PWB)</div>
                </td>
                <td style="padding-top: 60px;">
                    <p style="padding: 0;margin: 0;">Agung Nugroho</p>
                    <div>(Warehouse Supervisor)</div>
                </td>
                <td style="padding-top: 60px;">
                    <p style="padding: 0;margin: 0;">Chocky Hasian Simanjuntak</p>
                    <div>(Warehouse Manager)</div>
                </td>
            </tr>
        </table>
    </div>
</body>

</html>