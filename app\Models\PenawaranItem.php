<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PenawaranItem extends Model
{
    use HasFactory;

    protected $primaryKey = 'id';

    protected $fillable = [
        'penawaran_id',
        'part_inventory_id',
        'nama_part',
        'quantity',
        'price',
        'status',
    ];

    public function penawaran()
    {
        return $this->belongsTo(Penawaran::class);
    }

    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }
}
