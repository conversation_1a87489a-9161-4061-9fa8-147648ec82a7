<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Use the penawaran number in the title if available, otherwise a generic title -->
    <title>Penawaran <?php echo e($penawaran->nomor ?? ''); ?></title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            margin: 20px;
            /* Add some margin around the page */
            padding: 0;
            font-size: 14px;
            /* Slightly smaller base font size to match image */
            color: #000;
            /* Black text */
        }

        .header-container {
            padding-bottom: 1px;
            border-bottom: 2px solid black;
            /* First thick line */
            margin-bottom: 1px;
            /* Space before the second line */
        }

        .header-flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo-container {
            width: fit-content;
        }

        .logo {
            padding: 0px;
            height: 70px;
            display: block;
        }

        .company-details {
            text-align: center;
        }

        .company-name {
            color: #000080;
            /* Dark Blue */
            margin: 0;
            font-size: 22px;
            /* Adjusted font size */
            font-weight: bold;
        }

        .company-tagline {
            margin: 4px 0;
            font-size: 13px;
            /* Adjusted font size */
            font-weight: bold;
            /* Added bold */
        }

        .company-address {
            font-size: 9px;
            /* Adjusted font size */
            text-align: center;
            margin: 0;
        }

        .header-second-line {
            height: 0;
            border-bottom: 1px solid black;
            margin-bottom: 1px;
        }

        .footer-second-line {
            height: 0;
            border-bottom: 1px solid black;
            margin-bottom: 1px;
        }

        .document-info-container {
            display: flex;
            justify-content: space-between;
        }

        .info-right {
            width: 40%;
            /* Adjusted width */
            text-align: left;
            /* Align text left as per image */
        }

        .info-table {
            border: none;
            width: 100%;
            border-collapse: collapse;
        }

        .info-table td {
            border: none;
            padding: 0;
            margin: 0;
        }

        .info-label {
            width: 50px;
            /* Width for "Nomor", "Hal" */
            font-weight: bold;
            padding-right: 5px;
        }

        .info-right p {
            margin: 1px 0;
        }

        table.items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0px;
            /* Reduce space after table */
            font-size: 10.5px;
            /* Slightly smaller font in table */
        }

        table.items-table td {
            padding: 0;
            margin: 0;
        }

        table.items-table thead th {
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            background-color: #f2f2f2;
            text-align: center;
            font-weight: bold;
            white-space: nowrap;
        }

        table.items-table tbody td {
            border-bottom: 0;
            vertical-align: top;
        }

        table.items-table tfoot td {
            border-top: 0;
            font-weight: normal;
        }

        table.items-table tfoot tr:last-child td {
            font-weight: bold;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .notes-section {
            margin-top: 5px;
            font-size: 10.5px;
        }

        .notes-section strong {
            font-size: 14px;
            /* Make "Note" slightly larger */
        }

        .notes-section ul {
            margin: 3px 0 0 0;
            padding-left: 15px;
            /* Indent list items */
            list-style-type: '- ';
            /* Use dash for bullet points */
        }

        .notes-section li {
            margin-bottom: 2px;
            /* Space between notes */
            padding-left: 5px;
            /* Space after dash */
        }

        .closing-remarks {
            margin-top: 15px;
        }

        .closing-remarks p {
            margin: 4px 0;
        }

        .footer {
            margin-top: 25px;
            display: flex;
            justify-content: space-between;
            line-height: 1.4;
        }

        .signature {
            width: 40%;
            /* Adjust width */
            text-align: left;
            /* Left align signature block */
        }

        .signature p {
            margin: 0;
        }

        .signature-space {
            height: 50px;
            /* Space for manual signature */
        }

        .signature-name {
            font-weight: bold;
        }

        /* Use specific class for item description column */
        .col-description {
            text-align: left;
            white-space: nowrap;
            width: 30%;
            max-width: fit-content;
        }

        .pnumber {
            text-align: left;
            padding-left: 10px;
            white-space: nowrap;
            width: 25%;
            max-width: fit-content;
        }


        .col-no {
            width: 5%;
            text-align: center;
        }

        .col-qty {
            width: 8%;
            text-align: center;
        }

        .col-unit {
            width: 8%;
            text-align: center;
        }

        .col-price {
            width: 17%;
            text-align: right;
        }

        .col-total {
            width: 17%;
            text-align: right;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="header-container" style="padding-bottom: 0 !important;">
        <table style="width: 610px; border-collapse: collapse;">
            <tr>
                <td style="width: 60px; vertical-align: middle;">
                    <img src="<?php echo e(public_path('assets/images/logo-small.png')); ?>" alt="Logo" style="height: 60px;">
                </td>
                <td style="width: 100%; text-align: center; vertical-align: middle;">
                    <h1 style="margin: 0; font-size: 22px; color: #000080;">PT. PUTERA WIBOWO BORNEO</h1>
                    <p style="margin: 0; font-weight: bold;">TYRE - AIR CONDITIONING - FABRICATION</p>
                    <p style="margin: 0; font-size: 9px;">Address : Jl Palam Raya No. 10B Kel. Guntung Manggis, Kec.
                        Landasan Ulin Banjarbaru, Kalimantan Selatan Kode Pos 70721</p>
                </td>
            </tr>
        </table>
    </div>
    <div class="footer-second-line"></div>
    <!-- Document Info and Recipient -->
    <div class="document-info-container">
        <div class="info-left">
            <table class="info-table">
                <tr>
                    <td></td>
                    <td style="width: 300px;"></td>
                    <td style="text-align: left;">
                        <p style="padding: 0; margin: 0;">Banjarbaru, <?php echo e($date ?? '03 Januari 2025'); ?></p>
                    </td>
                </tr>
                <tr>
                    <td style="width: 2px;">Nomor</td>
                    <td style="max-width: fit-content;">: <?php echo e($penawaran->nomor ?? ''); ?></td>
                    <td></td>
                </tr>
                <tr>
                    <td style="max-width: 2px;">Hal</td>
                    <td style="width: 300px;">: <?php echo e($penawaran->perihal ?? ''); ?></td>
                    <td></td>
                </tr>
                <tr>
                    <td></td>
                    <td></td>
                    <td style="align-items: right; text-align: left; width: fit-content;">
                        <div>
                            <p style="padding: 0; margin: 0;">Kepada Yth :</p>
                            <p style="padding: 0; margin: 0; width: max-content; text-transform: uppercase;">
                                <strong><?php echo e($penawaran->customer ?? ''); ?> </strong>
                            </p>
                            <p style="padding: 0; margin: 0;"><strong>Attn :
                                    <?php echo e($penawaran->attn ?? 'Ibu Firda'); ?></strong></p>
                            <!-- Added fallback for preview -->
                            <p style="padding: 0; margin: 0;">Di -</p>
                            <p style="padding-left: 20px; margin: 0;">Tempat</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Salutation and Introduction -->
    <div class="salutation">
        <p style="margin: 1px; padding: 0;">Dengan Hormat,</p>
        <p style="margin: 1px; padding: 0;">Bersama ini kami sampaikan <strong><?php echo e($penawaran->perihal); ?></strong>
            Sebagai Berikut :</p>
    </div>

    <!-- Items Table -->
    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th class="col-no">No</th>
                <th class="col-description">Deskripsi</th>
                <?php if($penawaran->showcode == 1): ?>
                    <th class="pnumber">Kode Part</th>
                <?php endif; ?>
                <th class="col-qty">Qty</th>
                <th class="col-unit">OUM</th>
                <th class="col-price">Harga Satuan</th>
                <th class="col-total">Total</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $penawaran->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr <?php if($index === 0): ?> style="border-top: 1px solid #000;" <?php endif; ?>>
                    <td class="text-center" <?php if($index === 0): ?> style="padding-top: 8px;" <?php endif; ?>><?php echo e($index + 1); ?></td>
                    <td class="col-description" <?php if($index === 0): ?> style="padding-top: 8px;" <?php endif; ?>>
                        <?php echo e(strtoupper($item->nama_part ?: ($item->partInventory->site_part_name ?: strtoupper($item->partInventory->part->part_name)))); ?>

                    </td>
                    <?php if($penawaran->showcode == 1): ?>
                        <td class="pnumber" <?php if($index === 0): ?> style="padding-top: 8px;padding-left: 10px;" <?php else: ?>
                        style="padding-left: 10px;" <?php endif; ?>>
                            <?php echo e($item->partInventory->part->part_code); ?>

                        </td>
                    <?php endif; ?>
                    <td class="text-center" <?php if($index === 0): ?> style="padding-top: 8px;" <?php endif; ?>>
                        <?php echo e($item->quantity); ?>

                    </td>
                    <td class="text-center" <?php if($index === 0): ?> style="padding-top: 8px;" <?php endif; ?>>
                        <?php echo e($item->partInventory->part->eum); ?>

                    </td>
                    <td class="total-row" <?php if($index === 0): ?> style="padding-top: 8px;" <?php endif; ?>>
                        <table width="100%">
                            <tr>
                                <td align="left">Rp</td>
                                <td align="right" style="padding-right: 30px;">
                                    <?php echo e(number_format($item->price, 0, ',', '.')); ?>

                                </td>
                            </tr>
                        </table>
                    </td>
                    <td class="total-row" <?php if($index === 0): ?> style="padding-top: 8px;" <?php endif; ?>>
                        <table width="100%">
                            <tr>
                                <td align="left">Rp</td>
                                <td align="right"><?php echo e(number_format($item->quantity * $item->price, 0, ',', '.')); ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
        <tfoot>
            <?php
                if (!isset($subtotal) && isset($penawaran->items)) {
                    $subtotal = 0;
                    foreach ($penawaran->items as $item) {
                        $subtotal += $item->quantity * $item->price;
                    }
                } else {
                    $subtotal = $subtotal ?? 7325000;
                }

                $diskon = $diskon ?? ($penawaran->diskon ?? 0);
                $diskonAmount = $subtotal * ($diskon / 100);
                $subtotalAfterDiscount = $subtotal - $diskonAmount;
                $tax = $subtotalAfterDiscount * 0.11;
                $grandTotal = $subtotalAfterDiscount + $tax;
                $colspan = $penawaran->showcode == 1 ? 6 : 5;
            ?>

            <tr>
                <td colspan="<?php echo e($colspan); ?>" style="text-align: right;">Subtotal =</td>
                <td class="total-row" style="border-top: 1px solid #000;">
                    <table width="100%">
                        <tr>
                            <td align="left">Rp</td>
                            <td align="right"><?php echo e(number_format($subtotal, 0, ',', '.')); ?></td>
                        </tr>
                    </table>
                </td>
            </tr>

            <?php if($diskon > 0): ?>
                <tr>
                    <td colspan="<?php echo e($colspan); ?>" style="text-align: right;">Diskon
                        <?php echo e($diskon); ?>% =
                    </td>
                    <td class="total-row">
                        <table width="100%">
                            <tr>
                                <td align="left">Rp</td>
                                <td align="right"><?php echo e(number_format($diskonAmount, 0, ',', '.')); ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="<?php echo e($colspan); ?>" style="text-align: right;">Subtotal setelah diskon =</td>
                    <td class="total-row" style="border-top: 1px solid #000;">
                        <table width="100%">
                            <tr>
                                <td align="left">Rp</td>
                                <td align="right"><?php echo e(number_format($subtotalAfterDiscount, 0, ',', '.')); ?></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            <?php endif; ?>

            <tr>
                <td colspan="<?php echo e($colspan); ?>" style="text-align: right;">PPN =</td>
                <td class="total-row">
                    <table width="100%">
                        <tr>
                            <td align="left">Rp</td>
                            <td align="right"><?php echo e(number_format($tax, 0, ',', '.')); ?></td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td colspan="<?php echo e($colspan); ?>" style="text-align: right;"><strong>Grand Total =</strong></td>
                <td class="total-row" style="border-top: 1px solid #000;">
                    <table width="100%">
                        <tr>
                            <td align="left"><strong>Rp</strong></td>
                            <td align="right"><strong><?php echo e(number_format($grandTotal, 0, ',', '.')); ?></strong></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </tfoot>
    </table>


    <!-- Notes Section -->
    <div class="notes-section">
        <p style="margin-bottom: 1px;"><strong>Note :</strong></p>
        <ul>
            <!-- Dynamic notes from variable -->
            <?php if(!empty($penawaran->notes)): ?>
                <?php echo nl2br(e($penawaran->notes)); ?>

            <?php endif; ?>
        </ul>
    </div>
    <!-- Closing Remarks -->
    <div class="closing-remarks">
        <p>Demikian penawaran ini kami sampaikan. Tanggapan Bapak / Ibu kami tunggu.</p>
        <p>Atas perhatian dan kerjasamanya, kami ucapkan terima kasih</p>
    </div>
    <div class="header-second-line"></div>
    <table width="100%" style="padding-top: 0; margin-top: 0;">
        <tr>
            <td align="left" style=" position: relative;">
                <div style="position: relative;">
                    <img src="<?php echo e(public_path('assets/images/ttdtika.png')); ?>" alt=""
                        style="position: absolute; left: -15;  width: 200px; margin: 0; padding: 0; max-width: 80px;">
                    <!-- <img src="<?php echo e(public_path('assets/images/stempel.png')); ?>" alt=""
                        style="position: absolute;  opacity: 0.6; max-width: 100px;"> -->
                </div>
                <p>Best Regards,</p>
                <br><br>
                <p style="text-decoration: underline; margin: 0; font-family: serif;">Eryctica Binti R.</p>
            </td>

            <td align="right" style="width: 50%;">
                <p>Disetujui Oleh,</p>
                <br><br><br>
                <p style="text-decoration: underline; margin: 0;">Customer</p>
            </td>
        </tr>
    </table>
</body>

</html><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/sales/penawaran-pdf.blade.php ENDPATH**/ ?>