@extends('warehouse.content')
@section('contentho')
<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12 mb-3">
            <div class="bgwhite shadow-kit rounded-lg p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="font-weight-bold">Detail Penawaran</h4>
                    <div>
                        <a href="{{ route('warehouse.penawaran.index') }}" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" width="30%">Nomor Penawaran</th>
                                <td>{{ $penawaran->nomor }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">Perihal</th>
                                <td>{{ $penawaran->perihal }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">Customer</th>
                                <td>{{ $penawaran->customer }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">Attn</th>
                                <td>{{ $penawaran->attn ?: '-' }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">Lokasi</th>
                                <td>{{ $penawaran->lokasi }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">Tanggal</th>
                                <td>{{ $penawaran->created_at->format('d/m/Y') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="shadow-kit h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Status Penawaran</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>Status:</h6>
                                    @php
                                        $statusClass = '';
                                        switch($penawaran->status) {
                                            case 'Draft':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Dikirim ke customer':
                                                $statusClass = 'bg-info';
                                                break;
                                            case 'PO customer':
                                                $statusClass = 'bg-primary';
                                                break;
                                            case 'Proses penyediaan':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Selesai':
                                                $statusClass = 'bg-success';
                                                break;
                                        }
                                    @endphp
                                    <span class="badge {{ $statusClass }} text-white">{{ $penawaran->status }}</span>
                                    <p class="text-muted mt-2 small">Status penawaran hanya dapat diubah oleh Sales</p>
                                </div>

                                <hr>

                                <div class="mt-3">
                                    <h6>Notes:</h6>
                                    @if($penawaran->notes)
                                        @if(strpos($penawaran->notes, "\n") !== false)
                                            <ul>
                                                @foreach(explode("\n", $penawaran->notes) as $note)
                                                    @if(trim($note) !== '')
                                                        <li>{{ $note }}</li>
                                                    @endif
                                                @endforeach
                                            </ul>
                                        @else
                                            <p>{{ $penawaran->notes }}</p>
                                        @endif
                                    @else
                                        <p class="text-muted">Tidak ada notes</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="bgwhite shadow-kit rounded-lg p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="font-weight-bold">Daftar Part <span class="badge bg-info">Update Status Disini</span></h4>
                    <div>
                        <form action="{{ route('warehouse.penawaran.update-all-items-status', $penawaran->id) }}" method="POST" class="d-inline">
                            @csrf
                            <div class="input-group">
                                <select name="status" class="form-select">
                                    <option value="Ready">Ready</option>
                                    <option value="In Order">In Order</option>
                                    <option value="Not Ready">Not Ready</option>
                                </select>
                                <button type="submit" class="btn btn-primary">Update Semua</button>
                            </div>
                        </form>
                        <div class="text-muted small mt-2">
                            <i class="mdi mdi-information-outline"></i> Periksa ketersediaan stock sebelum mengubah status
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th>No</th>
                                <th>Part Code</th>
                                <th>Part Name</th>
                                <th>Quantity</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $grandTotal = 0; @endphp
                            @foreach($penawaran->items as $index => $item)
                            @php
                                $total = $item->quantity * $item->price;
                                $grandTotal += $total;
                            @endphp
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $item->partInventory->part_code }}</td>
                                <td>
                                    <div>{{ $item->nama_part ?: $item->partInventory->part->part_name }}</div>
                                    @if($item->partInventory->site_part_name)
                                        <small class="text-muted">{{ $item->partInventory->site_part_name }}</small>
                                    @endif
                                </td>
                                <td>{{ $item->quantity }}</td>
                                <td>
                                    @php
                                        $stockClass = $item->partInventory->stock_quantity >= $item->quantity ? 'text-success' : 'text-danger';
                                    @endphp
                                    <span class="{{ $stockClass }} font-weight-bold">{{ $item->partInventory->stock_quantity }}</span>
                                    @if($item->partInventory->stock_quantity < $item->quantity)
                                        <span class="badge bg-danger">Kurang</span>
                                    @endif
                                </td>
                                <td>
                                    @php
                                        $statusClass = '';
                                        switch($item->status) {
                                            case 'Ready':
                                                $statusClass = 'bg-success';
                                                break;
                                            case 'In Order':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Not Ready':
                                                $statusClass = 'bg-danger';
                                                break;
                                        }
                                    @endphp
                                    <span class="badge {{ $statusClass }} text-white">{{ $item->status }}</span>
                                </td>
                                <td>
                                    <form action="{{ route('warehouse.penawaran.update-item-status', $penawaran->id) }}" method="POST">
                                        @csrf
                                        <input type="hidden" name="item_id" value="{{ $item->id }}"
                                        @if(app()->environment('local'))
                                        data-debug="Item ID: {{ $item->id }}"
                                        @endif
                                        >
                                        <div class="input-group input-group-sm">
                                            <select name="status" class="form-select form-select-sm">
                                                <option value="Ready" {{ $item->status == 'Ready' ? 'selected' : '' }}>Ready</option>
                                                <option value="In Order" {{ $item->status == 'In Order' ? 'selected' : '' }}>In Order</option>
                                                <option value="Not Ready" {{ $item->status == 'Not Ready' ? 'selected' : '' }}>Not Ready</option>
                                            </select>
                                            <button type="submit" class="btn btn-sm btn-primary">Update</button>
                                        </div>
                                    </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            icon: 'success',
            title: 'Berhasil',
            text: "{{ session('success') }}",
        });
    });
</script>
@endif

@if(session('error'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: "{{ session('error') }}",
        });
    });
</script>
@endif
@endsection
