<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Berita Acara Pemakaian Part</title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 30px;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 15px;
        }

        .logo-left {
            float: left;
            width: 20%;
        }

        .title-center {
            float: left;
            width: 60%;
            text-align: center;
        }

        .number-right {
            float: right;
            width: 16%;
            text-align: right;
        }

        .header-title {
            background-color: rgb(29, 87, 138);
            color: white;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        /* Customer info table */
        .customer-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
        }

        .customer-table td {
            border: 1px solid #000;
            padding: 1px;
            padding-left: 4px;
        }

        .customer-table .label {
            width: 10%;
            font-weight: normal;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            margin-top: 15px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color: rgb(29, 87, 138);
            color: white;
            font-weight: bold;
        }

        /* Total section */
        .total-section {
            width: 30%;
            float: right;
            margin-bottom: 20px;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
        }

        .total-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: left;
        }

        .total-table .total-label {
            font-weight: bold;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .signature-table td {
            width: 50%;
            text-align: center;
            vertical-align: bottom;
            font-weight: bold;
            font-size: 10px;
        }

        .signature-stamp {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        /* Adjust for landscape orientation */
        @page {
            size: landscape;
        }
    </style>
</head>

<body>
    <!-- Header with logo, title and number -->
    <div class="header-container clearfix">
        <div class="logo-left" style="text-align: center;">
            <img src="<?php echo e(public_path('assets/images/logo-small.png')); ?>" alt="PWB LOGO" style="width: 80px;">
            <p><strong>PT PUTERA WIBOWO BORNEO</strong></p>
        </div>
        <div class="title-center">
            <div class="header-title">BERITA ACARA PEMAKAIAN PART SITE TRB (DELIVERY ORDER) <br> SITE PT. TRB- DMC</div>
        </div>
        <div class="number-right" style="padding-top: 26px;">
            <div
                style="font-size: 14px; font-weight: bold; border: 0px solid #000; padding: 4px; text-align: center; background-color:rgb(29, 87, 138); color: white;">
                <?php echo e($transactions[0]->do_number ?? ''); ?>

            </div>
        </div>
    </div>


    <!-- Customer Information -->
    <table class="customer-table">
        <tr style="width: 20%;">
            <td class="label">CUSTOMER</td>
            <td>PT. TANJUNG RAYA BERSAMA</td>
            <td class="label">CONTACT</td>
            <td style="width: 20%;"><?php echo e($transactions[0]->contact ?? ''); ?></td>
        </tr>
        <tr style="width: 100%;">
            <td class="label">CODE AREA</td>
            <td>SITE DMC</td>
            <td class="label">PHONE</td>
            <td><?php echo e($transactions[0]->phone ?? ''); ?></td>
        </tr>
        <tr>
            <td class="label">PAYMENT</td>
            <td>30 DAYS</td>
            <td class="label">DATE CREATED</td>
            <td style="width: 20%;">
                <?php echo e(\Carbon\Carbon::parse($transactions[0]->do_date ?? \Carbon\Carbon::now())->locale('id')->isoFormat('D MMMM Y')); ?>

            </td>
        </tr>
    </table>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width:6%;">NO</th>
                <th style="width:10%;">DATE</th>
                <th style="width:13%;">UNIT</th>
                <th style="width:20%;">PART NUMBER</th>
                <th style="width:28%;">DESCRIPTION</th>
                <th style="width:4%;">QTY</th>
                <th style="width:4%;">UOM</th>
                <th style="width:10%;">UNIT PRICE</th>
                <th style="width:13%;">TOTAL PRICE</th>
            </tr>
        </thead>

        <tbody>
            <?php if(count($transactions) > 0): ?>
                <?php
                    $no = 1;
                    $totalAmount = 0;
                    $currentWO = null;
                    $currentDate = null;
                    $currentUnit = null;
                    $woRowspan = 0;
                    $dateRowspan = 0;
                    $unitRowspan = 0;
                    $groupedData = [];

                    // First, group the data by WO, Date, and Unit
                    foreach ($transactions as $transaction) {
                        $do_date = $transaction->do_date ? \Carbon\Carbon::parse($transaction->do_date)->locale('id')->isoFormat('D MMMM Y') : '';
                        $woKey = $transaction->wo_number ?? '';
                        $dateKey = $do_date;
                        $unitKey = $transaction->unit->unit_code ?? '';

                        // Create a unique key for this combination
                        $groupKey = $woKey . '|' . $dateKey . '|' . $unitKey;

                        if (!isset($groupedData[$groupKey])) {
                            $groupedData[$groupKey] = [
                                'wo' => $woKey,
                                'date' => $dateKey,
                                'unit' => $unitKey,
                                'parts' => [],
                                'rowspan' => 0
                            ];
                        }
                        foreach ($transaction->parts as $part) {
                            $subtotal = $part->price * $part->quantity;
                            $totalAmount += $subtotal;

                            $groupedData[$groupKey]['parts'][] = [
                                'part_code' => $part->partInventory->part->part_code,
                                'part_name' => $part->partInventory->part->part_name,
                                'quantity' => $part->quantity,
                                'eum' => $part->partInventory->oum ?? $part->eum,
                                'price' => $part->price,
                                'subtotal' => $subtotal,
                                'id' => $part->id,
                            ];
                            $groupedData[$groupKey]['rowspan']++;
                        }
                        // After all parts for the current $groupKey are added, sort them by 'id'
                        if (isset($groupedData[$groupKey]['parts']) && is_array($groupedData[$groupKey]['parts'])) {
                            usort($groupedData[$groupKey]['parts'], function ($a, $b) {
                                return $a['id'] <=> $b['id'];
                            });
                        }
                    }
                ?>

                <?php $__currentLoopData = $groupedData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php $__currentLoopData = $group['parts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $part): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($no++); ?></td>
                            <td><?php echo e($group['date']); ?></td>
                            <td><?php echo e($group['unit']); ?></td>

                            <td><?php echo e($part['part_code']); ?></td>
                            <td style="text-align: center;"><?php echo e($part['part_name']); ?></td>
                            <td><?php echo e($part['quantity']); ?></td>
                            <td><?php echo e($part['eum']); ?></td>
                            <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span>
                                <?php echo e(number_format($part['price'], 0, ',', '.')); ?></td>
                            <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span>
                                <?php echo e(number_format($part['subtotal'], 0, ',', '.')); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <!-- Add empty rows to make the table look complete -->
                <?php for($i = $no; $i <= 10; $i++): ?>
                    <tr>
                        <td><?php echo e($i); ?></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                <?php endfor; ?>
            <?php else: ?>
                <?php for($i = 1; $i <= 10; $i++): ?>
                    <tr>
                        <td><?php echo e($i); ?></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                <?php endfor; ?>
            <?php endif; ?>
            <tr>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="text-align: left;">TOTAL</td>
                <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span>
                    <?php echo e(number_format($totalAmount ?? 0, 0, ',', '.')); ?></td>
            </tr>
            <tr style="background-color: #ffff">
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="text-align: left;">PPN 11%</td>
                <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span>
                    <?php echo e(number_format(($totalAmount ?? 0) * 0.11, 0, ',', '.')); ?></td>
            </tr>
            <tr>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="border: none;"></td>
                <td style="text-align: left;">GRAND TOTAL</td>
                <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span>
                    <?php echo e(number_format(($totalAmount ?? 0) * 1.11, 0, ',', '.')); ?></td>
            </tr>
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="clearfix">
        <table class="signature-table">
            <tr>
                <td>
                    <div>Dibuat Oleh</div>
                </td>
                <td>
                    <div>Diterima Oleh</div>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 60px;">
                    <div>Admin Site PT. Putera Wibowo Borneo</div>
                </td>
                <td style="padding-top: 60px;">
                    <div>Logistik PT. Tanjung Raya Bersama</div>
                </td>
            </tr>
        </table>
    </div>
</body>

</html><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/units/reportTRB_BAPP.blade.php ENDPATH**/ ?>