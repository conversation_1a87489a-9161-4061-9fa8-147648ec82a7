<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\Penawaran;
use App\Models\PenawaranItem;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\Site;
use App\Models\LogAktivitas;
use App\Models\Invoice;
use App\Models\CustomerSales;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;

class PenawaranController extends Controller
{
    public function index(Request $request)
    {
        // Get filter parameters
        $month = $request->input('month', now()->month);
        $year = $request->input('year', now()->year);
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');
        $customerFilter = $request->input('customer');

        // Create a query for penawarans
        $query = Penawaran::with('items.partInventory.part')
            ->orderBy('created_at', 'desc');

        // Apply date range filter if provided
        if ($dateFrom && $dateTo) {
            $query->where(function ($q) use ($dateFrom, $dateTo) {
                $q->whereBetween('tanggal_penawaran', [$dateFrom, $dateTo])
                    ->orWhereBetween('created_at', [$dateFrom, $dateTo]);
            });
        }
        // Otherwise apply month and year filter if provided
        else if ($month && $year) {
            $query->where(function ($q) use ($month, $year) {
                $q->whereMonth('created_at', $month)
                    ->whereYear('created_at', $year)
                    ->orWhere(function ($q2) use ($month, $year) {
                        $q2->whereMonth('tanggal_penawaran', $month)
                            ->whereYear('tanggal_penawaran', $year);
                    });
            });
        }

        // Apply customer filter if provided
        if ($customerFilter) {
            $query->where('customer', 'like', '%' . $customerFilter . '%');
        }

        // Get the filtered penawarans
        $penawarans = $query->paginate(100);

        // Calculate total for each penawaran
        foreach ($penawarans as $penawaran) {
            $total = 0;#ffffff
            foreach ($penawaran->items as $item) {
                $total += $item->quantity * $item->price;
            }
            $penawaran->total = $total;
        }

        // If no data found for the selected month, find the latest month with data
        if ($penawarans->isEmpty() && $month && $year) {
            // Get the latest month that has data
            $latestData = Penawaran::select(DB::raw('MONTH(created_at) as month, YEAR(created_at) as year'))
                ->orderBy('created_at', 'desc')
                ->first();

            if ($latestData) {
                $month = $latestData->month;
                $year = $latestData->year;

                // Re-query with the latest month that has data
                $penawarans = Penawaran::with('items.partInventory.part')
                    ->where(function ($q) use ($month, $year) {
                        $q->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year)
                            ->orWhere(function ($q2) use ($month, $year) {
                                $q2->whereMonth('tanggal_penawaran', $month)
                                    ->whereYear('tanggal_penawaran', $year);
                            });
                    })
                    ->orderBy('created_at', 'desc')
                    ->paginate(100);

                // Calculate total for each penawaran
                foreach ($penawarans as $penawaran) {
                    $total = 0;
                    foreach ($penawaran->items as $item) {
                        $total += $item->quantity * $item->price;
                    }
                    $penawaran->total = $total;
                }
            }
        }

        // Get all available months with data for the dropdown (combining created_at and tanggal_penawaran)
        $createdAtMonths = Penawaran::select(DB::raw('DISTINCT MONTH(created_at) as month, YEAR(created_at) as year'))
            ->whereNotNull('created_at');

        $tanggalPenawaranMonths = Penawaran::select(DB::raw('DISTINCT MONTH(tanggal_penawaran) as month, YEAR(tanggal_penawaran) as year'))
            ->whereNotNull('tanggal_penawaran');

        $availableMonths = $createdAtMonths->union($tanggalPenawaranMonths)
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        return view('sales.penawaran', compact(
            'penawarans',
            'month',
            'year',
            'availableMonths',
            'dateFrom',
            'dateTo',
            'customerFilter'
        ));
    }

    public function store(Request $request)
    {
        // Log the entire request for debugging
        Log::info('Penawaran store request received', [
            'request_data' => $request->all()
        ]);

        // Basic validation for common fields
        $basicRules = [
            'nomor' => 'required|string|unique:penawarans,nomor',
            'tanggal_penawaran' => 'nullable|date',
            'perihal' => 'required|string',
            'customer' => 'required|string',
            'attn' => 'nullable|string',
            'lokasi' => 'required|string',
            'showcode' => 'required|string',
            'notes' => 'nullable|string',
            'diskon' => 'nullable|numeric|min:0|max:100',
            'parts' => 'required|array',
        ];

        $validator = \Validator::make($request->all(), $basicRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate each part separately
        foreach ($request->parts as $index => $part) {
            // Log part data for debugging
            Log::info('Validating part at index ' . $index, [
                'part_data' => $part,
                'is_custom' => isset($part['is_custom']) ? $part['is_custom'] : 'not set',
                'is_custom_type' => isset($part['is_custom']) ? gettype($part['is_custom']) : 'N/A',
                'purchase_price' => isset($part['purchase_price']) ? $part['purchase_price'] : 'not set',
                'purchase_price_type' => isset($part['purchase_price']) ? gettype($part['purchase_price']) : 'N/A',
                'part_name' => isset($part['part_name']) ? $part['part_name'] : 'not set',
                'nama_part' => isset($part['nama_part']) ? $part['nama_part'] : 'not set',
                'part_inventory_id' => isset($part['part_inventory_id']) ? $part['part_inventory_id'] : 'not set'
            ]);

            $partRules = [
                "parts.{$index}.quantity" => 'required|integer|min:1',
                "parts.{$index}.price" => 'required|numeric|min:0',
            ];

            // Check if it's a custom part by is_custom flag or negative part_inventory_id
            $isCustomPart = (isset($part['is_custom']) && ($part['is_custom'] === true || $part['is_custom'] === 'true' || $part['is_custom'] === 1 || $part['is_custom'] === '1')) ||
                (isset($part['part_inventory_id']) && $part['part_inventory_id'] < 0) ||
                (isset($part['part_code']) && strpos($part['part_code'], 'CUSTOM-') === 0);

            if ($isCustomPart) {
                $partRules["parts.{$index}.part_code"] = 'required|string';
                $partRules["parts.{$index}.part_name"] = 'required|string';
                $partRules["parts.{$index}.part_type"] = 'nullable|string|in:AC,TYRE,FABRIKASI,PERLENGKAPAN AC,PERSEDIAAN LAINNYA';
                $partRules["parts.{$index}.purchase_price"] = 'nullable|numeric|min:0';
                $partRules["parts.{$index}.eum"] = 'nullable|string|max:5';

                Log::info('Part ' . $index . ' is custom, validating part_code, part_name, and additional fields');
            } else {
                // If it's a regular part, validate part_inventory_id
                $partRules["parts.{$index}.part_inventory_id"] = 'required|exists:part_inventories,part_inventory_id';
                // For regular parts, part_name and nama_part are optional since they come from inventory
                $partRules["parts.{$index}.part_name"] = 'nullable|string';
                $partRules["parts.{$index}.nama_part"] = 'nullable|string';

                Log::info('Part ' . $index . ' is regular, validating part_inventory_id');
            }

            $partValidator = \Validator::make($request->all(), $partRules);

            if ($partValidator->fails()) {
                Log::error('Part validation failed for index ' . $index, [
                    'errors' => $partValidator->errors()->toArray(),
                    'part_data' => $part,
                    'validation_rules' => $partRules,
                    'is_custom_part' => $isCustomPart,
                    'part_inventory_id' => $part['part_inventory_id'] ?? 'not set',
                    'part_name' => $part['part_name'] ?? 'not set',
                    'nama_part' => $part['nama_part'] ?? 'not set'
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Validasi part gagal pada index ' . $index . '. Error: ' . $partValidator->errors()->first(),
                    'errors' => $partValidator->errors(),
                    'debug_info' => [
                        'part_data' => $part,
                        'validation_rules' => $partRules,
                        'is_custom_part' => $isCustomPart
                    ]
                ], 422);
            }
        }

        try {
            DB::beginTransaction();

            // Set default date to today if not provided
            $tanggalPenawaran = $request->tanggal_penawaran ? $request->tanggal_penawaran : now()->toDateString();

            $penawaran = Penawaran::create([
                'nomor' => $request->nomor,
                'tanggal_penawaran' => $tanggalPenawaran,
                'perihal' => $request->perihal,
                'customer' => $request->customer,
                'customer_code' => $request->customer_code,
                'attn' => $request->attn,
                'showcode' => $request->showcode,
                'lokasi' => $request->lokasi,
                'notes' => $request->notes,
                'diskon' => $request->diskon ?? 0, // Store as percentage (5, 10, etc.)
                'status' => 'Draft',
            ]);

            foreach ($request->parts as $part) {
                // Check if it's a custom part
                if (isset($part['is_custom']) && $part['is_custom']) {
                    $partInventory = $this->createCustomPartWithWHOInventory($part);

                    if ($partInventory) {
                        try {
                            // Create penawaran item with the part inventory
                            PenawaranItem::create([
                                'penawaran_id' => $penawaran->id,
                                'part_inventory_id' => $partInventory->part_inventory_id,
                                'nama_part' => $part['part_name'] ?? $part['nama_part'] ?? null,
                                'quantity' => $part['quantity'],
                                'price' => $part['price'],
                                'status' => 'Not Ready', // Default status is Not Ready
                            ]);
                        } catch (\Exception $e) {
                            Log::error('Error creating penawaran item for custom part: ' . $e->getMessage());
                            // Continue even if we can't create the penawaran item
                        }
                    }
                } else {
                    try {
                        // For regular parts, get the part name from inventory if not provided
                        $namaPart = $part['nama_part'] ?? $part['part_name'] ?? null;

                        // If nama_part is still null or empty, get it from the inventory
                        if (empty($namaPart) && isset($part['part_inventory_id'])) {
                            $partInventory = \App\Models\PartInventory::with('part')->find($part['part_inventory_id']);
                            if ($partInventory && $partInventory->part) {
                                $namaPart = $partInventory->part->part_name;
                            }
                        }

                        // For regular parts, create penawaran item as usual
                        PenawaranItem::create([
                            'penawaran_id' => $penawaran->id,
                            'part_inventory_id' => $part['part_inventory_id'],
                            'nama_part' => $namaPart,
                            'quantity' => $part['quantity'],
                            'price' => $part['price'],
                            'status' => 'Not Ready', // Default status is Not Ready
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error creating penawaran item for regular part: ' . $e->getMessage());
                        // Continue even if we can't create the penawaran item
                    }
                }
            }

            // Log the activity
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Membuat Penawaran',
                'description' => "User " . session('name') . " membuat Penawaran: " . $request->nomor . " untuk " . $request->customer,
                'table' => "Penawarans",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Penawaran berhasil disimpan']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating penawaran: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Gagal menyimpan penawaran: ' . $e->getMessage()], 500);
        }
    }

    public function show($id)
    {
        $penawaran = Penawaran::with('items.partInventory.part')->findOrFail($id);

        // Convert to array first to prevent Carbon object serialization issues
        $penawaranArray = $penawaran->toArray();

        // Get raw date values from database without timezone conversion to prevent -1 day offset
        try {
            // Query the database directly to get raw date strings without Carbon casting
            $rawPenawaran = DB::table('penawarans')->where('id', $penawaran->id)->first(['tanggal_penawaran']);

            if ($rawPenawaran && $rawPenawaran->tanggal_penawaran) {
                // Extract just the date part (YYYY-MM-DD) from the raw database value
                $penawaranArray['tanggal_penawaran'] = substr($rawPenawaran->tanggal_penawaran, 0, 10);
            }

            Log::info('Raw date override successful for penawaran - tanggal_penawaran: ' . $penawaranArray['tanggal_penawaran']);
        } catch (\Exception $dateException) {
            Log::error('Error getting raw dates for penawaran: ' . $dateException->getMessage());
            // Fallback to Carbon formatting if raw values are not available
            if ($penawaran->tanggal_penawaran) {
                $penawaranArray['tanggal_penawaran'] = $penawaran->tanggal_penawaran->format('Y-m-d');
            }
        }

        return response()->json($penawaranArray);
    }

    public function update(Request $request, $id)
    {
        // Basic validation for common fields
        $basicRules = [
            'nomor' => 'required|string|unique:penawarans,nomor,' . $id,
            'tanggal_penawaran' => 'nullable|date',
            'perihal' => 'required|string',
            'customer' => 'required|string',
            'attn' => 'nullable|string',
            'showcode' => 'nullable|string',
            'lokasi' => 'required|string',
            'notes' => 'nullable|string',
            'parts' => 'required|array',
        ];

        $validator = \Validator::make($request->all(), $basicRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate each part separately
        foreach ($request->parts as $index => $part) {
            // Log part data for debugging
            Log::info('Validating part at index ' . $index, [
                'part_data' => $part,
                'is_custom' => isset($part['is_custom']) ? $part['is_custom'] : 'not set',
                'is_custom_type' => isset($part['is_custom']) ? gettype($part['is_custom']) : 'N/A'
            ]);

            $partRules = [
                "parts.{$index}.quantity" => 'required|integer|min:1',
                "parts.{$index}.price" => 'required|numeric|min:0',
            ];

            // Check if it's a custom part by is_custom flag or negative part_inventory_id
            $isCustomPart = (isset($part['is_custom']) && ($part['is_custom'] === true || $part['is_custom'] === 'true' || $part['is_custom'] === 1 || $part['is_custom'] === '1')) ||
                (isset($part['part_inventory_id']) && $part['part_inventory_id'] < 0) ||
                (isset($part['part_code']) && strpos($part['part_code'], 'CUSTOM-') === 0);

            if ($isCustomPart) {
                $partRules["parts.{$index}.part_code"] = 'required|string';
                $partRules["parts.{$index}.part_name"] = 'required|string';
                $partRules["parts.{$index}.part_type"] = 'nullable|string|in:AC,TYRE,FABRIKASI,PERLENGKAPAN AC,PERSEDIAAN LAINNYA';
                $partRules["parts.{$index}.purchase_price"] = 'nullable|numeric|min:0';
                $partRules["parts.{$index}.eum"] = 'nullable|string|max:5';

                Log::info('Part ' . $index . ' is custom, validating part_code, part_name, and additional fields');
            } else {
                // If it's a regular part, validate part_inventory_id
                $partRules["parts.{$index}.part_inventory_id"] = 'required|exists:part_inventories,part_inventory_id';
                // For regular parts, part_name and nama_part are optional since they come from inventory
                $partRules["parts.{$index}.part_name"] = 'nullable|string';
                $partRules["parts.{$index}.nama_part"] = 'nullable|string';

                Log::info('Part ' . $index . ' is regular, validating part_inventory_id');
            }

            $partValidator = \Validator::make($request->all(), $partRules);

            if ($partValidator->fails()) {
                Log::error('Part validation failed for index ' . $index, [
                    'errors' => $partValidator->errors()->toArray()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Validasi part gagal',
                    'errors' => $partValidator->errors()
                ], 422);
            }
        }

        try {
            DB::beginTransaction();

            $penawaran = Penawaran::findOrFail($id);

            // Set default date to today if not provided
            $tanggalPenawaran = $request->tanggal_penawaran ? $request->tanggal_penawaran : ($penawaran->tanggal_penawaran ?? now()->toDateString());

            $penawaran->update([
                'nomor' => $request->nomor,
                'tanggal_penawaran' => $tanggalPenawaran,
                'perihal' => $request->perihal,
                'customer' => $request->customer,
                'customer_code' => $request->customer_code,
                'attn' => $request->attn,
                'lokasi' => $request->lokasi,
                'showcode' => $request->showcode,
                'notes' => $request->notes,
                'diskon' => $request->diskon ?? $penawaran->diskon ?? 0, // Store as percentage (5, 10, etc.)
                'status' => $request->status ?? $penawaran->status,
            ]);

            // Delete existing items
            $penawaran->items()->delete();

            // Create new items
            foreach ($request->parts as $part) {
                // Check if it's a custom part
                if (isset($part['is_custom']) && $part['is_custom']) {
                    $partInventory = $this->createCustomPartWithWHOInventory($part);

                    if ($partInventory) {
                        try {
                            // Create penawaran item with the part inventory
                            PenawaranItem::create([
                                'penawaran_id' => $penawaran->id,
                                'part_inventory_id' => $partInventory->part_inventory_id,
                                'nama_part' => $part['part_name'] ?? $part['nama_part'] ?? null,
                                'quantity' => $part['quantity'],
                                'price' => $part['price'],
                                'status' => $part['status'] ?? 'Not Ready', // Default status is Not Ready
                            ]);
                        } catch (\Exception $e) {
                            Log::error('Error creating penawaran item for custom part: ' . $e->getMessage());
                            // Continue even if we can't create the penawaran item
                        }
                    }
                } else {
                    try {
                        // For regular parts, get the part name from inventory if not provided
                        $namaPart = $part['nama_part'] ?? $part['part_name'] ?? null;

                        // If nama_part is still null or empty, get it from the inventory
                        if (empty($namaPart) && isset($part['part_inventory_id'])) {
                            $partInventory = \App\Models\PartInventory::with('part')->find($part['part_inventory_id']);
                            if ($partInventory && $partInventory->part) {
                                $namaPart = $partInventory->part->part_name;
                            }
                        }

                        // For regular parts, create penawaran item as usual
                        PenawaranItem::create([
                            'penawaran_id' => $penawaran->id,
                            'part_inventory_id' => $part['part_inventory_id'],
                            'nama_part' => $namaPart,
                            'quantity' => $part['quantity'],
                            'price' => $part['price'],
                            'status' => $part['status'] ?? 'Not Ready', // Default status is Not Ready
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error creating penawaran item for regular part: ' . $e->getMessage());
                        // Continue even if we can't create the penawaran item
                    }
                }
            }

            // Log the activity
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Mengubah Penawaran',
                'description' => "User " . session('name') . " mengubah Penawaran: " . $request->nomor . " untuk " . $request->customer,
                'table' => "Penawarans",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Penawaran berhasil diperbarui']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating penawaran: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Gagal memperbarui penawaran: ' . $e->getMessage()], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $penawaran = Penawaran::findOrFail($id);

            // Log the activity before deleting
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Penawaran',
                'description' => "User " . session('name') . " menghapus Penawaran: " . $penawaran->nomor . " untuk " . $penawaran->customer,
                'table' => "Penawarans",
                'ip_address' => request()->ip(),
            ]);

            $penawaran->delete();
            return response()->json(['success' => true, 'message' => 'Penawaran berhasil dihapus']);
        } catch (\Exception $e) {
            Log::error('Error deleting penawaran: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Gagal menghapus penawaran: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get the next penawaran number suggestion
     */
    public function getNextNumber()
    {
        try {
            // Get the current month and year
            $currentMonth = now()->format('m');
            $currentYear = now()->format('Y');

            // Get the month in Roman numerals
            $romanMonths = [
                '01' => 'I',
                '02' => 'II',
                '03' => 'III',
                '04' => 'IV',
                '05' => 'V',
                '06' => 'VI',
                '07' => 'VII',
                '08' => 'VIII',
                '09' => 'IX',
                '10' => 'X',
                '11' => 'XI',
                '12' => 'XII'
            ];
            $romanMonth = $romanMonths[$currentMonth];

            // Find the latest penawaran with a number matching the pattern
            $pattern = '%/%/' . $romanMonth . '/' . $currentYear;
            $latestPenawaran = Penawaran::where('nomor', 'like', $pattern)
                ->orderBy('created_at', 'desc')
                ->first();

            $nextNumber = 1;

            if ($latestPenawaran) {
                // Extract the number from the latest penawaran
                $parts = explode('/', $latestPenawaran->nomor);
                if (count($parts) > 0 && is_numeric($parts[0])) {
                    $nextNumber = (int)$parts[0] + 1;
                }
            }

            // Format the number with leading zeros (e.g., 001)
            $formattedNumber = str_pad($nextNumber, 3, '0', STR_PAD_LEFT);

            // Create the suggested number
            $suggestedNumber = $formattedNumber . '/SK-PWB/' . $romanMonth . '/' . $currentYear;

            return response()->json([
                'success' => true,
                'next_number' => $suggestedNumber
            ]);
        } catch (\Exception $e) {
            Log::error('Error generating next penawaran number: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghasilkan nomor penawaran: ' . $e->getMessage()
            ], 500);
        }
    }

    public function searchParts(Request $request)
    {
        $search = $request->input('search');
        $limit = $request->input('limit', 10);

        // Query ALL parts from the main parts table with LEFT JOIN to WHO inventory for pricing
        $parts = Part::leftJoin('part_inventories', function ($join) {
            $join->on('parts.part_code', '=', 'part_inventories.part_code')
                ->where('part_inventories.site_id', '=', 'WHO');
        })
            ->where(function ($query) use ($search) {
                $query->where('parts.part_name', 'like', "%{$search}%")
                    ->orWhere('parts.part_code', 'like', "%{$search}%")
                    ->orWhere('part_inventories.site_part_name', 'like', "%{$search}%");
            })
            ->select(
                'parts.part_code',
                'parts.part_name',
                'parts.part_type',
                'parts.price as main_price',
                'parts.eum',
                'part_inventories.part_inventory_id',
                'part_inventories.site_part_name',
                'part_inventories.price as who_price',
                'part_inventories.stock_quantity'
            )
            ->limit($limit)
            ->get()
            ->map(function ($part) {
                // Use main parts table price if available, otherwise fallback to WHO price
                $finalPrice = $part->main_price ?: ($part->who_price ?: 0);

                return [
                    'part_inventory_id' => $part->part_inventory_id,
                    'part_code' => $part->part_code,
                    'part_name' => $part->part_name,
                    'site_part_name' => $part->site_part_name ?: $part->part_name,
                    'price' => $finalPrice,
                    'stock_quantity' => $part->stock_quantity ?: 0,
                    'part_type' => $part->part_type,
                    'eum' => $part->eum ?: 'EA'
                ];
            });

        return response()->json($parts);
    }

    public function generatePdf($id)
    {
        try {
            $penawaran = Penawaran::with('items.partInventory.part')->findOrFail($id);

            // Calculate totals
            $subtotal = 0;
            foreach ($penawaran->items as $item) {
                // Calculate item total
                $itemTotal = $item->quantity * $item->price;
                $subtotal += $itemTotal;
            }

            // Calculate tax (11%)
            $tax = $subtotal * 0.11;
            $grandTotal = $subtotal + $tax;

            // Format the date in Indonesian format
            // Use tanggal_penawaran if available, otherwise fall back to created_at
            $dateToUse = $penawaran->tanggal_penawaran ?? $penawaran->created_at;
            $date = Carbon::parse($dateToUse)->format('d F Y');

            // Convert month to Indonesian
            $indonesianMonths = [
                'January' => 'Januari',
                'February' => 'Februari',
                'March' => 'Maret',
                'April' => 'April',
                'May' => 'Mei',
                'June' => 'Juni',
                'July' => 'Juli',
                'August' => 'Agustus',
                'September' => 'September',
                'October' => 'Oktober',
                'November' => 'November',
                'December' => 'Desember'
            ];

            foreach ($indonesianMonths as $english => $indonesian) {
                $date = str_replace($english, $indonesian, $date);
            }

            // Generate PDF
            $pdf = PDF::loadView('sales.penawaran-pdf', [
                'penawaran' => $penawaran,
                'date' => $date,
                'subtotal' => $subtotal,
                'tax' => $tax,
                'grandTotal' => $grandTotal
            ]);

            // Set paper size to A4
            $pdf->setPaper('a4');

            // Generate filename - sanitize by replacing slashes with underscores
            $sanitizedNomor = str_replace(['/', '\\'], '_', $penawaran->nomor);
            $filename = 'Penawaran_' . $sanitizedNomor . '.pdf';

            // Return PDF for download
            return $pdf->stream($filename);
        } catch (\Exception $e) {
            Log::error('Error generating penawaran PDF: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Gagal membuat PDF: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Generate invoice from penawaran data
     */
    public function generateInvoice($id)
    {
        try {
            $penawaran = Penawaran::with(['items.partInventory.part', 'invoice'])->findOrFail($id);

            // Check if penawaran has an invoice
            if (!$penawaran->invoice) {
                return response()->view('errors.custom', [
                    'message' => 'Invoice belum dibuat. Silakan buat invoice terlebih dahulu.',
                    'back_url' => '/sales/penawaran'
                ], 404);
            }

            $invoice = $penawaran->invoice;
            $invoiceNumber = $invoice->no_invoice;

            // Calculate totals
            $subtotal = 0;
            $allParts = [];
            $diskonPersen = $penawaran->diskon ?? 0;

            foreach ($penawaran->items as $item) {
                // Hitung total item
                $itemTotal = $item->quantity * $item->price;

                // Terapkan diskon jika ada (berdasarkan persentase)
                if ($diskonPersen > 0) {
                    $itemTotal -= ($itemTotal * $diskonPersen / 100);
                }

                $subtotal += $itemTotal;

                // Tambahkan ke list
                $allParts[] = [
                    'part_name' => $item->nama_part ?: ($item->partInventory->site_part_name ?: $item->partInventory->part->part_name),
                    'quantity' => $item->quantity,
                    'price' => $item->price -= $item->price * $diskonPersen / 100,
                    'total' => $itemTotal,
                    'eum' => $item->partInventory->part->eum ?? 'EA'
                ];
            }

            // Hitung PPN (11%) dari subtotal yang sudah didiskon
            $ppnRate = 0.11;
            $ppn = $subtotal * $ppnRate;
            $grandTotal = $subtotal + $ppn;

            // Format untuk ditampilkan
            $formattedSubtotal = number_format($subtotal, 0, ',', '.');
            $formattedPpn = number_format($ppn, 0, ',', '.');
            $formattedGrandTotal = number_format($grandTotal, 0, ',', '.');

            // Convert grand total to words in Indonesian
            $terbilang = $this->terbilang($grandTotal);

            // Log the invoice generation
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Cetak Invoice Penawaran',
                'description' => 'Invoice ' . $invoiceNumber . ' dicetak dari Penawaran ' . $penawaran->nomor,
                'table' => 'Invoices',
                'ip_address' => request()->ip(),
            ]);

            // Generate PDF using the dedicated invoice-penawaran template
            $pdf = PDF::loadView('sales.invoice-penawaran', compact(
                'penawaran',
                'invoice',
                'terbilang',
                'invoiceNumber',
                'allParts',
                'formattedSubtotal',
                'formattedPpn',
                'formattedGrandTotal',
                'subtotal',
                'ppnRate',
                'ppn'
            ));

            // Set paper size to portrait A4
            $pdf->setPaper('a4', 'portrait');

            // Sanitize invoice number for filename
            $safeInvoiceNumber = str_replace(['/', '\\'], '_', $invoiceNumber);

            // Stream the PDF (show in browser)
            return $pdf->stream('invoice_' . $safeInvoiceNumber . '_' . Carbon::now()->format('YmdHis') . '.pdf');
        } catch (\Exception $e) {
            Log::error('Error generating invoice from penawaran: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->view('errors.custom', [
                'message' => 'Terjadi kesalahan saat membuat invoice. Silakan coba lagi.',
                'error' => $e->getMessage(),
                'back_url' => '/sales/penawaran'
            ], 500);
        }
    }

    /**
     * Get invoice data for a penawaran
     */
    public function getInvoiceData($id)
    {
        try {
            Log::info('getInvoiceData called for penawaran ID: ' . $id);
            $penawaran = Penawaran::with('invoice')->findOrFail($id);
            // Check if penawaran has an invoice
            if ($penawaran->invoice) {
                Log::info('Penawaran has invoice: ', ['invoice_id' => $penawaran->invoice->id]);
                return response()->json([
                    'success' => true,
                    'has_invoice' => true,
                    'invoice' => $penawaran->invoice,
                    'penawaran' => $penawaran
                ]);
            } else {
                Log::info('Penawaran does not have invoice');

                // Return penawaran data for new invoice
                return response()->json([
                    'success' => true,
                    'has_invoice' => false,
                    'penawaran' => $penawaran
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error getting invoice data: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Gagal mendapatkan data invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update status of penawaran and related invoice
     */
    public function updateStatus(Request $request)
    {
        try {
            // Validate the request
            $validatedData = $request->validate([
                'penawaran_id' => 'required|exists:penawarans,id',
                'current_status' => 'required|string',
                'new_status' => 'required|string',
                'invoice_notes' => 'nullable|string',
                'invoice_documents' => 'nullable|array',
                'invoice_documents.*' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx'
            ], [
                'invoice_documents.*.max' => 'Ukuran file tidak boleh lebih dari 10MB',
                'invoice_documents.*.mimes' => 'Format file harus pdf, jpg, jpeg, png, doc, atau docx'
            ]);

            DB::beginTransaction();

            // Get the penawaran
            $penawaran = Penawaran::with('invoice')->findOrFail($validatedData['penawaran_id']);

            // Update penawaran status
            $penawaran->status = $validatedData['new_status'];
            $penawaran->save();

            // If status is "Selesai" and there's an invoice, update invoice status and add documents
            if ($validatedData['new_status'] === 'Selesai' && $penawaran->invoice) {
                $invoice = $penawaran->invoice;

                // Update invoice status
                $invoice->status = 'Selesai';
                $invoice->payment_status = 'Lunas';
                $invoice->payment_date = now();

                // Add notes if provided
                if (!empty($validatedData['invoice_notes'])) {
                    $invoice->payment_notes = $validatedData['invoice_notes'];
                }

                // Handle document upload - single document that replaces the old one
                if ($request->hasFile('invoice_documents') && count($request->file('invoice_documents')) > 0) {
                    // Get the first file (we only use one document now)
                    $file = $request->file('invoice_documents')[0];

                    // Delete old file if it exists
                    if ($invoice->document_path && file_exists(public_path('assets/invoice_documents/' . $invoice->document_path))) {
                        unlink(public_path('assets/invoice_documents/' . $invoice->document_path));
                    }

                    // Upload new file
                    $fileName = time() . '_' . $file->getClientOriginalName();
                    $file->move(public_path('assets/invoice_documents'), $fileName);

                    // Save document path as a string (not JSON anymore)
                    $invoice->document_path = $fileName;
                }

                $invoice->save();
            }

            // Log the activity
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Ubah Status Penawaran',
                'description' => "User " . session('name') . " mengubah status Penawaran " . $penawaran->nomor . " dari " . $validatedData['current_status'] . " menjadi " . $validatedData['new_status'],
                'table' => "Penawarans",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Status penawaran berhasil diperbarui'
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating penawaran status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal memperbarui status penawaran: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save invoice for penawaran
     */
    public function saveInvoice(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'penawaran_id' => 'required|exists:penawarans,id',
                'invoice_id' => 'nullable|exists:invoices,id',
                'model_unit' => 'required|string|max:255',
                'hmkm' => 'required|string|max:255',
                'sn' => 'required|string|max:255',
                'trouble' => 'required|string|max:255',
                'lokasi' => 'required|string|max:255',
                'location' => 'required|string|max:255',
                'transfer_to' => 'required|string|max:255',
                'bank_account' => 'required|string|max:255',
                'bank_branch' => 'required|string|max:255',
                'tanggal_invoice' => 'nullable|date',
                'no_invoice' => 'nullable|string|max:255',
                'po_number' => 'nullable|string|max:255',
                'notes' => 'nullable|string'
            ]);

            $penawaran = Penawaran::findOrFail($validatedData['penawaran_id']);

            // Ensure customer_code is available - try to find it if missing
            if (empty($penawaran->customer_code) && !empty($penawaran->customer)) {
                $customerSales = CustomerSales::where('nama_customer', $penawaran->customer)->first();
                if ($customerSales) {
                    // Update the penawaran with the found customer_code
                    $penawaran->update(['customer_code' => $customerSales->code]);
                    $penawaran->refresh(); // Refresh to get the updated data

                    Log::info('Found and updated missing customer_code for penawaran', [
                        'penawaran_id' => $penawaran->id,
                        'customer' => $penawaran->customer,
                        'found_customer_code' => $customerSales->code
                    ]);
                }
            }

            // Generate invoice number if new invoice
            if (empty($validatedData['invoice_id'])) {
                // Create new invoice
                $invoice = new Invoice();

                // Set invoice number if provided, otherwise generate one
                if (!empty($validatedData['no_invoice'])) {
                    $invoice->no_invoice = $validatedData['no_invoice'];
                } else {
                    // Get the last invoice number
                    $lastInvoice = Invoice::orderBy('id', 'desc')->first();
                    $lastNumber = 1;

                    if ($lastInvoice && preg_match('/^(\d+)\/INV-PWB\//', $lastInvoice->no_invoice, $matches)) {
                        $lastNumber = (int)$matches[1] + 1;
                    }

                    // Format: 001/INV-PWB/05/2025
                    $month = date('m');
                    $year = date('Y');
                    $invoiceNumber = sprintf('%03d/INV-PWB/%s/%s', $lastNumber, $month, $year);
                    $invoice->no_invoice = $invoiceNumber;
                }

                $invoice->penawaran_id = $penawaran->id;
                $invoice->customer = $penawaran->customer;
                $invoice->customer_code = $penawaran->customer_code;
                $invoice->location = $penawaran->lokasi; // Set location field

                // Log customer_code assignment for new invoice
                Log::info('Creating new invoice with customer_code', [
                    'penawaran_id' => $penawaran->id,
                    'customer' => $penawaran->customer,
                    'customer_code' => $penawaran->customer_code,
                    'customer_code_is_null' => is_null($penawaran->customer_code)
                ]);
                $invoice->lokasi = $validatedData['lokasi']; // Also set lokasi field
                $invoice->location = $validatedData['location']; // Also set lokasi field
                $invoice->tanggal_invoice = $validatedData['tanggal_invoice'] ?? now();
                $invoice->due_date = Carbon::parse($validatedData['tanggal_invoice'] ?? now())->addDays(30);
                $invoice->ppn = 11; // 11% PPN
                $invoice->payment_status = 'Belum Lunas';
                $invoice->status = 'Draft';

                // Set PO number if provided
                if (!empty($validatedData['po_number'])) {
                    $invoice->po_number = $validatedData['po_number'];
                }
            } else {
                // Update existing invoice
                $invoice = Invoice::findOrFail($validatedData['invoice_id']);

                // Update customer and customer_code from penawaran to maintain referential integrity
                $invoice->customer = $penawaran->customer;
                $invoice->customer_code = $penawaran->customer_code;

                // Log customer_code assignment for existing invoice update
                Log::info('Updating existing invoice with customer_code', [
                    'invoice_id' => $invoice->id,
                    'penawaran_id' => $penawaran->id,
                    'old_customer' => $invoice->getOriginal('customer'),
                    'new_customer' => $penawaran->customer,
                    'old_customer_code' => $invoice->getOriginal('customer_code'),
                    'new_customer_code' => $penawaran->customer_code,
                    'customer_code_is_null' => is_null($penawaran->customer_code)
                ]);

                // Update invoice number if provided
                if (!empty($validatedData['no_invoice'])) {
                    $invoice->no_invoice = $validatedData['no_invoice'];
                }

                // Update PO number if provided
                if (!empty($validatedData['po_number'])) {
                    $invoice->po_number = $validatedData['po_number'];
                }
            }

            // Update invoice fields
            $invoice->model_unit = $validatedData['model_unit'];
            $invoice->hmkm = $validatedData['hmkm'];
            $invoice->sn = $validatedData['sn'];
            $invoice->trouble = $validatedData['trouble'];
            $invoice->lokasi = $validatedData['lokasi']; // Update lokasi field
            $invoice->location = $validatedData['location']; // Also update location field to match
            $invoice->transfer_to = $validatedData['transfer_to'];
            $invoice->bank_account = $validatedData['bank_account'];
            $invoice->bank_branch = $validatedData['bank_branch'];
            $invoice->notes = $validatedData['notes'];

            // Update invoice date if provided
            if (isset($validatedData['tanggal_invoice'])) {
                $invoice->tanggal_invoice = $validatedData['tanggal_invoice'];
                // Also update due date based on the new invoice date
                $invoice->due_date = Carbon::parse($validatedData['tanggal_invoice'])->addDays(30);
            }

            // Save invoice
            $invoice->save();

            // Log activity
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => empty($validatedData['invoice_id']) ? 'Buat Invoice Penawaran' : 'Update Invoice Penawaran',
                'description' => 'Invoice ' . $invoice->no_invoice . ' untuk Penawaran ' . $penawaran->nomor,
                'table' => 'Invoices',
                'ip_address' => request()->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Invoice berhasil disimpan',
                'invoice' => $invoice,
                'invoice_url' => route('sales.penawaran.invoice', $penawaran->id)
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error saving invoice: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal menyimpan invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert number to Indonesian words
     */
    private function terbilang($number)
    {
        $number = abs($number);
        $words = ["", "satu", "dua", "tiga", "empat", "lima", "enam", "tujuh", "delapan", "sembilan", "sepuluh", "sebelas"];

        if ($number < 12) {
            return $words[$number];
        } elseif ($number < 20) {
            return $words[$number - 10] . " belas";
        } elseif ($number < 100) {
            return $words[floor($number / 10)] . " puluh " . $words[$number % 10];
        } elseif ($number < 200) {
            return "seratus " . $this->terbilang($number - 100);
        } elseif ($number < 1000) {
            return $words[floor($number / 100)] . " ratus " . $this->terbilang($number % 100);
        } elseif ($number < 2000) {
            return "seribu " . $this->terbilang($number - 1000);
        } elseif ($number < 1000000) {
            return $this->terbilang(floor($number / 1000)) . " ribu " . $this->terbilang($number % 1000);
        } elseif ($number < 1000000000) {
            return $this->terbilang(floor($number / 1000000)) . " juta " . $this->terbilang($number % 1000000);
        } elseif ($number < 1000000000000) {
            return $this->terbilang(floor($number / 1000000000)) . " milyar " . $this->terbilang($number % 1000000000);
        } elseif ($number < 1000000000000000) {
            return $this->terbilang(floor($number / 1000000000000)) . " trilyun " . $this->terbilang($number % 1000000000000);
        }

        return "";
    }

    /**
     * Create a custom part and automatically insert it into WHO inventory
     *
     * @param array $partData The part data containing part_code, part_name, price, and other fields
     * @return PartInventory|null The created part inventory record or null if failed
     */
    private function createCustomPartWithWHOInventory($partData)
    {
        try {
            // Log incoming part data for debugging
            Log::info('Creating custom part with WHO inventory', [
                'incoming_data' => $partData,
                'purchase_price_raw' => isset($partData['purchase_price']) ? $partData['purchase_price'] : 'NOT SET',
                'purchase_price_type' => isset($partData['purchase_price']) ? gettype($partData['purchase_price']) : 'N/A'
            ]);

            // Validate required fields
            if (empty($partData['part_code']) || empty($partData['part_name']) || !isset($partData['price'])) {
                Log::error('Missing required fields for custom part creation', $partData);
                return null;
            }

            // Extract part data with defaults
            $partType = $partData['part_type'] ?? 'PERSEDIAAN LAINNYA';
            $purchasePrice = $partData['purchase_price'] ?? 0;
            $eum = $partData['eum'] ?? 'EA';
            $binLocation = '-'; // Default bin location for custom parts

            // Log extracted values
            Log::info('Extracted part data values', [
                'part_type' => $partType,
                'purchase_price' => $purchasePrice,
                'eum' => $eum,
                'bin_location' => $binLocation
            ]);

            // Check if the part already exists in the parts table
            $existingPart = Part::where('part_code', $partData['part_code'])->first();

            if (!$existingPart) {
                // Create a new part record in the main parts table
                $newPart = Part::create([
                    'part_code' => $partData['part_code'],
                    'part_name' => $partData['part_name'],
                    'part_type' => $partType,
                    'price' => $partData['price'],
                    'purchase_price' => $purchasePrice,
                    'eum' => $eum,
                    'bin_location' => $binLocation
                ]);

                Log::info('Created new part in main parts table', [
                    'part_code' => $partData['part_code'],
                    'part_name' => $partData['part_name'],
                    'part_type' => $partType,
                    'price' => $partData['price'],
                    'purchase_price' => $purchasePrice,
                    'eum' => $eum
                ]);
            } else {
                Log::info('Part already exists in main parts table', [
                    'part_code' => $partData['part_code']
                ]);
            }

            // Check if WHO inventory record already exists
            $existingWHOInventory = PartInventory::where('part_code', $partData['part_code'])
                ->where('site_id', 'WHO')
                ->first();

            if ($existingWHOInventory) {
                Log::info('WHO inventory record already exists for part', [
                    'part_code' => $partData['part_code'],
                    'part_inventory_id' => $existingWHOInventory->part_inventory_id
                ]);
                return $existingWHOInventory;
            }

            // Create WHO inventory record for the custom part
            $partInventory = PartInventory::create([
                'part_code' => $partData['part_code'],
                'site_part_name' => $partData['part_name'],
                'site_id' => 'WHO',
                'price' => $partData['price'],
                'priority' => false,
                'min_stock' => 0,
                'max_stock' => 0,
                'stock_quantity' => 0,
            ]);

            Log::info('Successfully created WHO inventory record for custom part', [
                'part_code' => $partData['part_code'],
                'part_inventory_id' => $partInventory->part_inventory_id,
                'site_part_name' => $partData['part_name'],
                'price' => $partData['price']
            ]);

            // Verify that the part was created with purchase_price
            $createdPart = Part::where('part_code', $partData['part_code'])->first();
            if ($createdPart) {
                Log::info('Verification - Part created with data:', [
                    'part_code' => $createdPart->part_code,
                    'part_name' => $createdPart->part_name,
                    'part_type' => $createdPart->part_type,
                    'price' => $createdPart->price,
                    'purchase_price' => $createdPart->purchase_price,
                    'eum' => $createdPart->eum,
                    'bin_location' => $createdPart->bin_location
                ]);
            }

            // Log activity for custom part creation
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Membuat Part Custom',
                'description' => "User " . session('name') . " membuat part custom: " . $partData['part_code'] . " - " . $partData['part_name'] . " dan menambahkannya ke inventory WHO",
                'table' => "Parts & PartInventories",
                'ip_address' => request()->ip(),
            ]);

            return $partInventory;
        } catch (\Exception $e) {
            Log::error('Error creating custom part with WHO inventory', [
                'error' => $e->getMessage(),
                'part_data' => $partData,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Test endpoint to verify part data
     */
    public function testPartData($partCode)
    {
        $part = Part::where('part_code', $partCode)->first();

        if (!$part) {
            return response()->json([
                'success' => false,
                'message' => 'Part not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'part' => [
                'part_code' => $part->part_code,
                'part_name' => $part->part_name,
                'part_type' => $part->part_type,
                'price' => $part->price,
                'purchase_price' => $part->purchase_price,
                'eum' => $part->eum,
                'bin_location' => $part->bin_location,
                'created_at' => $part->created_at,
                'updated_at' => $part->updated_at
            ]
        ]);
    }
}
